"""
向量存储模块
使用ChromaDB存储文档向量，支持语义检索
"""
import os
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import uuid

try:
    import chromadb
    from chromadb.config import Settings
    CHROMADB_AVAILABLE = True
except ImportError:
    chromadb = None
    CHROMADB_AVAILABLE = False
except Exception:
    chromadb = None
    CHROMADB_AVAILABLE = False

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SentenceTransformer = None
    SENTENCE_TRANSFORMERS_AVAILABLE = False
except Exception:
    SentenceTransformer = None
    SENTENCE_TRANSFORMERS_AVAILABLE = False

from .document_processor import DocumentChunk
from ..utils.config import get_config
from ..utils.logger import get_logger
from ..utils.helpers import get_optimal_device, ProgressTracker, get_memory_usage, resource_path

logger = get_logger(__name__)


class VectorStore:
    """向量存储管理器"""
    
    def __init__(self):
        self.config = get_config()
        self.client = None
        self.collection = None
        self.embedding_model = None

        # 资源管理
        self._model_loaded = False
        self._client_initialized = False

        # 检查依赖是否可用
        if not CHROMADB_AVAILABLE:
            logger.warning("ChromaDB不可用，向量搜索功能将受限")
            self.device = "cpu"
            return

        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            logger.warning("SentenceTransformers不可用，向量搜索功能将受限")
            self.device = "cpu"
            return

        try:
            self.device = get_optimal_device()
            # 初始化
            self._initialize_client()
            self._load_embedding_model()
        except Exception as e:
            logger.error(f"向量存储初始化失败: {e}")
            self.device = "cpu"
    
    def _initialize_client(self):
        """初始化ChromaDB客户端"""
        if not CHROMADB_AVAILABLE or chromadb is None:
            logger.warning("ChromaDB不可用，跳过客户端初始化")
            return
        
        try:
            # 确保持久化目录存在
            persist_dir = Path(self.config.database.chroma_persist_dir)
            persist_dir.mkdir(parents=True, exist_ok=True)

            # 尝试不同的ChromaDB初始化方式
            try:
                # 方式1：使用新版本的设置
                self.client = chromadb.PersistentClient(
                    path=str(persist_dir),
                    settings=Settings(
                        anonymized_telemetry=False,
                        allow_reset=True
                    )
                )
            except Exception as e1:
                logger.warning(f"新版本ChromaDB初始化失败，尝试兼容模式: {e1}")
                try:
                    # 方式2：使用基本设置
                    self.client = chromadb.PersistentClient(
                        path=str(persist_dir)
                    )
                except Exception as e2:
                    logger.warning(f"基本ChromaDB初始化失败，尝试内存模式: {e2}")
                    # 方式3：使用内存客户端作为回退
                    self.client = chromadb.Client()

            # 获取或创建集合
            collection_name = self.config.database.collection_name

            try:
                self.collection = self.client.get_collection(collection_name)
                logger.info(f"加载现有集合: {collection_name}")
            except Exception:
                self.collection = self.client.create_collection(
                    name=collection_name,
                    metadata={"description": "公司制度文档向量集合"}
                )
                logger.info(f"创建新集合: {collection_name}")

        except Exception as e:
            logger.error(f"ChromaDB初始化失败: {e}")
            # 设置为None，让系统继续运行但禁用向量搜索
            self.client = None
            self.collection = None
            logger.warning("向量存储初始化失败，将禁用语义搜索功能")
    
    def _load_embedding_model(self):
        """加载嵌入模型"""
        if SentenceTransformer is None:
            raise ImportError("sentence-transformers未安装，请运行: pip install sentence-transformers")
        
        try:
            model_name = self.config.model.embedding_model
            model_path = Path(resource_path(os.path.join(self.config.models_dir, model_name)))
            
            # 检查本地模型是否存在
            if model_path.exists():
                logger.info(f"加载本地嵌入模型: {model_path}")
                self.embedding_model = SentenceTransformer(str(model_path), device=self.device)
            else:
                logger.info(f"下载嵌入模型: {model_name}")
                self.embedding_model = SentenceTransformer(model_name, device=self.device)
                
                # 保存到本地
                model_path.mkdir(parents=True, exist_ok=True)
                self.embedding_model.save(str(model_path))
                logger.info(f"模型已保存到: {model_path}")
            
            logger.info(f"嵌入模型加载成功，设备: {self.device}")
            
        except Exception as e:
            logger.error(f"嵌入模型加载失败: {e}")
            raise
    
    def encode_texts(self, texts: List[str]) -> List[List[float]]:
        """编码文本为向量"""
        try:
            if self.embedding_model is None:
                logger.error("嵌入模型未加载")
                raise ValueError("嵌入模型未加载")

            embeddings = self.embedding_model.encode(
                texts,
                convert_to_tensor=False,
                show_progress_bar=len(texts) > 10
            )
            return embeddings.tolist()
        except Exception as e:
            logger.error(f"文本编码失败: {e}")
            raise
    
    def add_chunks(self, chunks: List[DocumentChunk]) -> bool:
        """添加文档分块到向量库"""
        if not chunks:
            logger.warning("没有分块需要添加")
            return True
        
        try:
            logger.info(f"开始添加 {len(chunks)} 个分块到向量库")
            
            # 准备数据
            ids = []
            documents = []
            metadatas = []
            
            for chunk in chunks:
                ids.append(chunk.id)
                documents.append(chunk.content)
                metadatas.append(chunk.metadata)
            
            # 生成向量
            logger.info("生成文本向量...")
            embeddings = self.encode_texts(documents)
            
            # 添加到集合
            logger.info("添加到向量数据库...")
            self.collection.add(
                ids=ids,
                documents=documents,
                metadatas=metadatas,
                embeddings=embeddings
            )
            
            logger.info(f"成功添加 {len(chunks)} 个分块到向量库")
            return True
            
        except Exception as e:
            logger.error(f"添加分块到向量库失败: {e}")
            return False
    
    def search(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """语义搜索"""
        try:
            if self.collection is None or self.embedding_model is None:
                logger.warning("向量存储或嵌入模型不可用，跳过语义搜索")
                return []

            # 编码查询
            query_embedding = self.encode_texts([query])[0]

            # 搜索
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=top_k,
                include=['documents', 'metadatas', 'distances']
            )

            # 格式化结果
            search_results = []

            if results['documents'] and results['documents'][0]:
                for i in range(len(results['documents'][0])):
                    result = {
                        'content': results['documents'][0][i],
                        'metadata': results['metadatas'][0][i],
                        'distance': results['distances'][0][i],
                        'score': 1 - results['distances'][0][i]  # 转换为相似度分数
                    }
                    search_results.append(result)

            logger.info(f"语义搜索完成，返回 {len(search_results)} 个结果")
            return search_results

        except Exception as e:
            logger.error(f"语义搜索失败: {e}")
            return []

    def batch_search(self, queries: List[str], top_k: int = 5) -> Dict[str, List[Dict[str, Any]]]:
        """批量语义搜索"""
        try:
            if not queries:
                return {}

            # 批量编码查询
            query_embeddings = self.encode_texts(queries)

            # 批量搜索
            results = self.collection.query(
                query_embeddings=query_embeddings,
                n_results=top_k,
                include=['documents', 'metadatas', 'distances']
            )

            # 格式化结果
            batch_results = {}

            for i, query in enumerate(queries):
                search_results = []

                if (results['documents'] and i < len(results['documents']) and
                    results['documents'][i]):

                    for j in range(len(results['documents'][i])):
                        result = {
                            'content': results['documents'][i][j],
                            'metadata': results['metadatas'][i][j],
                            'distance': results['distances'][i][j],
                            'score': 1 - results['distances'][i][j]
                        }
                        search_results.append(result)

                batch_results[query] = search_results

            logger.info(f"批量语义搜索完成，处理 {len(queries)} 个查询")
            return batch_results

        except Exception as e:
            logger.error(f"批量语义搜索失败: {e}")
            return {query: [] for query in queries}
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """获取集合统计信息"""
        try:
            if self.collection is None:
                return {
                    'total_chunks': 0,
                    'collection_name': self.config.database.collection_name,
                    'embedding_model': self.config.model.embedding_model,
                    'status': 'disabled'
                }

            count = self.collection.count()
            return {
                'total_chunks': count,
                'collection_name': self.config.database.collection_name,
                'embedding_model': self.config.model.embedding_model,
                'status': 'active'
            }
        except Exception as e:
            logger.error(f"获取集合统计失败: {e}")
            return {
                'total_chunks': 0,
                'collection_name': self.config.database.collection_name,
                'embedding_model': self.config.model.embedding_model,
                'status': 'error'
            }
    
    def delete_by_source(self, source_file: str) -> bool:
        """根据源文件删除分块"""
        try:
            # 查询该文件的所有分块
            results = self.collection.get(
                where={"source_file": source_file},
                include=['ids']
            )
            
            if results['ids']:
                self.collection.delete(ids=results['ids'])
                logger.info(f"删除文件 {source_file} 的 {len(results['ids'])} 个分块")
                return True
            else:
                logger.info(f"文件 {source_file} 没有找到相关分块")
                return True
                
        except Exception as e:
            logger.error(f"删除分块失败: {e}")
            return False
    
    def clear_collection(self) -> bool:
        """清空集合"""
        try:
            # 删除现有集合
            collection_name = self.config.database.collection_name
            self.client.delete_collection(collection_name)
            
            # 重新创建集合
            self.collection = self.client.create_collection(
                name=collection_name,
                metadata={"description": "公司制度文档向量集合"}
            )
            
            logger.info("向量集合已清空")
            return True
            
        except Exception as e:
            logger.error(f"清空集合失败: {e}")
            return False
    
    def batch_add_chunks(self, chunks: List[DocumentChunk], batch_size: Optional[int] = None) -> bool:
        """批量添加分块（大数据量优化）"""
        if not chunks:
            return True

        # 动态计算批处理大小
        if batch_size is None:
            batch_size = self._calculate_optimal_batch_size(len(chunks))

        try:
            total_batches = (len(chunks) + batch_size - 1) // batch_size
            progress = ProgressTracker(total_batches, "向量化处理")

            for i in range(0, len(chunks), batch_size):
                batch_chunks = chunks[i:i + batch_size]

                # 监控内存使用
                memory_info = get_memory_usage()
                if memory_info['percent'] > 85:
                    logger.warning(f"内存使用率过高: {memory_info['percent']:.1f}%，减小批处理大小")
                    batch_size = max(10, batch_size // 2)

                if not self.add_chunks(batch_chunks):
                    logger.error(f"批次 {i//batch_size + 1} 处理失败")
                    return False

                progress.update()

            progress.finish()
            logger.info(f"批量添加完成，总共处理 {len(chunks)} 个分块")
            return True

        except Exception as e:
            logger.error(f"批量添加失败: {e}")
            return False

    def _calculate_optimal_batch_size(self, total_chunks: int) -> int:
        """计算最优批处理大小"""
        memory_info = get_memory_usage()
        available_memory_gb = memory_info.get('rss_mb', 1000) / 1024

        # 根据可用内存和数据量动态调整
        if available_memory_gb > 8:
            base_batch_size = 200
        elif available_memory_gb > 4:
            base_batch_size = 100
        else:
            base_batch_size = 50

        # 根据总数据量调整
        if total_chunks > 10000:
            batch_size = min(base_batch_size, 500)
        elif total_chunks > 1000:
            batch_size = min(base_batch_size, 200)
        else:
            batch_size = min(base_batch_size, 100)

        logger.info(f"计算得出最优批处理大小: {batch_size}")
        return batch_size
    
    def update_chunk(self, chunk_id: str, content: str, metadata: Dict[str, Any]) -> bool:
        """更新分块"""
        try:
            # ChromaDB不支持直接更新，需要先删除再添加
            self.collection.delete(ids=[chunk_id])
            
            embedding = self.encode_texts([content])[0]
            
            self.collection.add(
                ids=[chunk_id],
                documents=[content],
                metadatas=[metadata],
                embeddings=[embedding]
            )
            
            logger.info(f"分块 {chunk_id} 更新成功")
            return True
            
        except Exception as e:
            logger.error(f"更新分块失败: {e}")
            return False
    
    def get_chunk_by_id(self, chunk_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取分块"""
        try:
            results = self.collection.get(
                ids=[chunk_id],
                include=['documents', 'metadatas']
            )
            
            if results['documents'] and results['documents'][0]:
                return {
                    'id': chunk_id,
                    'content': results['documents'][0],
                    'metadata': results['metadatas'][0]
                }
            
            return None
            
        except Exception as e:
            logger.error(f"获取分块失败: {e}")
            return None
